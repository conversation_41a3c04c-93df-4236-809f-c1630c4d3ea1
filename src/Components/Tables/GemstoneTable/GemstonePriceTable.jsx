import React, { useState } from "react";
import DynamicRow from "../../DynamicRow/DynamicRow";

export default function GemstonePriceTable({
  tableHeading,
  gemstonePrices = [],
  setGemstonePrices,
  isEditing,
  handlePriceChange,
  category,
  onAddRow,
}) {
  const [isAddingRow, setIsAddingRow] = useState(false);

  const handleAddRow = async (newRow) => {
    if (!newRow.quality || newRow.ratePerCt === "") {
      return;
    }
  
    const rowData = {
      quality: newRow.quality.trim(),
      ratePerCt: parseFloat(newRow.ratePerCt) || 0,
    };
  
    try {
      const response = await onAddRow(rowData); // API Call
      if (response?.data?.success) {
        setGemstonePrices((prev) => [...prev, response.data.newRow]);
      }
    } catch (error) {
      console.error("Error adding row:", error);
    } finally {
      setIsAddingRow(false);
    }
  };
  
  
  return (
    <div className="overflow-x-auto flex flex-col items-center my-6">
      <table className="w-[70%] min-w-[600px] divide-y divide-gray-300 border border-gray-300 shadow-md">
        <thead className="bg-gray-100">
          {tableHeading && (
            <tr>
              <th colSpan="2" className="px-6 py-4 text-center text-lg font-semibold bg-gray-300">
                {tableHeading}
              </th>
            </tr>
          )}
          <tr>
            <th className="px-6 py-3 text-center">Gemstone Quality</th>
            <th className="px-6 py-3 text-center bg-yellow-100">Price (₹)</th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 bg-white text-center">
          {Array.isArray(gemstonePrices) &&
            gemstonePrices.map((item) => (
              <tr key={item._id}>
                <td className="p-4 text-center">{item.quality}</td>
                <td className="p-4 bg-yellow-50 text-center">
                  {isEditing ? (
                    <input
                      type="number"
                      className="w-20 p-2 border rounded-md text-center"
                      value={item.ratePerCt}
                      onChange={(e) => handlePriceChange(item._id, e.target.value, setGemstonePrices)}
                    />
                  ) : (
                    <span>₹{item.ratePerCt}</span>
                  )}
                </td>
              </tr>
            ))}

          {/* Render DynamicRow when isAddingRow is true */}
          {isAddingRow && (
            <tr>
              <td colSpan="2" className="p-4 text-center">
                <DynamicRow
                  columns={[
                    {
                      key: "quality",
                      placeholder: "Quality",
                      type: "text",
                    },
                    { key: "ratePerCt", placeholder: "Price (₹)", type: "number" },
                  ]}
                  onAddRow={handleAddRow}
                  onCancel={() => setIsAddingRow(false)}
                />
              </td>
            </tr>
          )}
        </tbody>
      </table>

      {/* Button to add a new row */}
      <button
        onClick={() => setIsAddingRow(true)}
        className="ml-4 cursor-pointer mt-4 p-2 border-gray-400 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300 transition"
      >
        + Add New Row
      </button>
    </div>
  );
}
