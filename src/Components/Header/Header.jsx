import React, { useState, useEffect, useRef } from "react";
import { Bars3Icon, UserCircleIcon, ArrowRightOnRectangleIcon } from "@heroicons/react/24/outline";
import { useAuth } from "../../Context/AuthContext";
import apiClient from "../../Utils/AxiosConfig";

const Header = () => {
  const { toggleSidebar, isDesktop, user, logout } = useAuth();
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const syncIntervalRef = useRef(null);

  // Function to check sync status
  const checkSyncStatus = async () => {
    try {
      const response = await apiClient.get("/sync-status");
      return response.data.data.syncStatus;
    } catch (error) {
      console.error("Failed to check sync status:", error);
      return false; // Assume sync is complete if we can't check status
    }
  };

  // Function to start continuous status checking
  const startStatusPolling = async () => {
    const pollStatus = async () => {
      // Check if polling should continue
      if (!syncIntervalRef.current) return;

      try {
        const syncStatus = await checkSyncStatus();
        if (syncStatus && syncIntervalRef.current) {
          // Sync is still in progress, trigger API again immediately
          setTimeout(pollStatus, 0);
        } else {
          // Sync is complete
          console.log("Sync completed, cleaning up...");
          setIsSyncing(false);
          syncIntervalRef.current = null;
          localStorage.removeItem('syncInProgress'); // Clear sync state
        }
      } catch (error) {
        console.error("Error during status polling:", error);
        // Stop polling on error
        setIsSyncing(false);
        syncIntervalRef.current = null;
        localStorage.removeItem('syncInProgress'); // Clear sync state on error
      }
    };

    // Start the polling chain
    syncIntervalRef.current = true; // Use as a flag to track if polling is active
    pollStatus();
  };

  // Check for ongoing sync on component mount
  useEffect(() => {
    const checkOngoingSync = async () => {
      const syncInProgress = localStorage.getItem('syncInProgress') === 'true';
      if (syncInProgress) {
        console.log("Resuming sync after page reload...");
        setIsSyncing(true);
        // Check current sync status and resume polling if needed
        try {
          const syncStatus = await checkSyncStatus();
          if (syncStatus) {
            // Sync is still in progress, resume polling
            startStatusPolling();
          } else {
            // Sync completed while page was reloading
            setIsSyncing(false);
            localStorage.removeItem('syncInProgress');
          }
        } catch (error) {
          console.error("Error checking sync status on mount:", error);
          setIsSyncing(false);
          localStorage.removeItem('syncInProgress');
        }
      }
    };

    checkOngoingSync();
  }, []);

  // Cleanup polling on component unmount
  useEffect(() => {
    return () => {
      if (syncIntervalRef.current) {
        syncIntervalRef.current = null; // Stop the polling chain
      }
    };
  }, []);

  const handleSyncClick = async () => {
    if (isSyncing) return; // Prevent multiple sync requests

    setIsSyncing(true);
    localStorage.setItem('syncInProgress', 'true'); // Persist sync state

    try {
      console.log("Starting sync...");
      await apiClient.post("/sync", {}, {
        headers: {
          "Content-Type": "application/json",
        },
      });

      // Start polling for sync status
      startStatusPolling();
    } catch (error) {
      console.error("Sync failed:", error);
      setIsSyncing(false); // Reset state if sync request fails
      localStorage.removeItem('syncInProgress'); // Clear sync state on failure
    }
  };

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      await logout();
    } catch (error) {
      console.error("Logout failed:", error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <div className="w-full p-[10px] h-[60px] bg-gray-200 shadow-md flex items-center justify-between">
      <div>
        {!isDesktop && (
          <button
            onClick={toggleSidebar}
            className="p-2 cursor-pointer rounded-md hover:bg-gray-300 transition"
          >
            <Bars3Icon className="h-6 w-6 text-gray-700" />
          </button>
        )}
      </div>

      <div className="flex items-center space-x-4">
        <button
          onClick={handleSyncClick}
          disabled={isSyncing}
          className={`px-4 py-2 rounded-md transition-colors duration-200 flex items-center space-x-2 ${
            isSyncing
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-blue-500 hover:bg-blue-600 cursor-pointer"
          } text-white`}
        >
          {isSyncing && (
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
          )}
          <span>{isSyncing ? "Syncing..." : "Sync Products"}</span>
        </button>

        {/* User info and logout */}
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <UserCircleIcon className="h-8 w-8 text-gray-700" />
            {user && (
              <div className="hidden md:block">
                <p className="text-sm font-medium text-gray-700">{user.name}</p>
                <p className="text-xs text-gray-500">{user.role}</p>
              </div>
            )}
          </div>

          <button
            onClick={handleLogout}
            disabled={isLoggingOut}
            className={`flex items-center space-x-1 px-4 py-2 rounded-md transition-colors duration-200 ${
              isLoggingOut
                ? "bg-gray-300 cursor-not-allowed"
                : "bg-gray-500 hover:bg-gray-600 text-white"
            }`}
          >
            <ArrowRightOnRectangleIcon className="h-4 w-4" />
            <span className="text-sm">
              {isLoggingOut ? "Logging out..." : "Logout"}
            </span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Header; 